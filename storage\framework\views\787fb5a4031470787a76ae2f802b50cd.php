

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12 col-md-12 col-lg-12">
            <div class="card shadow-sm mb-8">
                <div class="card-header">
                    
                </div>
                <?php
if (! isset($_instance)) {
    $html = \Livewire\Livewire::mount($livewire_component, $livewire_data ?? [])->html();
} elseif ($_instance->childHasBeenRendered('7VK9d5r')) {
    $componentId = $_instance->getRenderedChildComponentId('7VK9d5r');
    $componentTag = $_instance->getRenderedChildComponentTagName('7VK9d5r');
    $html = \Livewire\Livewire::dummyMount($componentId, $componentTag);
    $_instance->preserveRenderedChild('7VK9d5r');
} else {
    $response = \Livewire\Livewire::mount($livewire_component, $livewire_data ?? []);
    $html = $response->html();
    $_instance->logRenderedChild('7VK9d5r', $response->id(), \Livewire\Livewire::getRootElementTagName($html));
}
echo $html;
?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
    <?php echo \Livewire\Livewire::styles(); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <?php echo \Livewire\Livewire::scripts(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/layouts/livewire.blade.php ENDPATH**/ ?>