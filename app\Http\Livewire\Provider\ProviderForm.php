<?php

namespace App\Http\Livewire\Provider;

use App\Models\User;
use App\Models\State;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class ProviderForm extends Component
{
    use WithFileUploads;

    public $step = 1;
    public User $provider;
    public $editMode = false;

    // Form fields
    public $email, $first_name, $last_name, $printed_name, $clinic_name;
    public $npi, $lic, $dea;
    public $phone, $fax, $dispatch_method, $state, $address, $city, $state_id, $zip;
    public $dispense_abbreviation;
    public $signature;
    public $password, $password_confirmation;

    public $states = [];

    // Dynamic steps configuration
    public $steps = [];
    public $totalSteps = 4;

    public function mount($provider, $editMode = false, $steps = null)
    {
        $this->provider = $provider;
        $this->editMode = $editMode;
        $this->initializeSteps($steps);
        $this->loadStates();

        if ($this->editMode && $provider) {
            $this->provider = $provider;
            $this->loadProviderData();
        }
    }

    public function initializeSteps($customSteps = null)
    {
        if ($customSteps) {
            $this->steps = $customSteps;
            $this->totalSteps = count($customSteps);
        } else {
            // Default steps for provider form
            $this->steps = [
                1 => 'Basic Information',
                2 => 'Identification',
                3 => 'Contact and Address',
                4 => 'Signature Upload'
            ];
            $this->totalSteps = 4;
        }
    }

    public function loadStates()
    {
        $this->states = State::orderBy('name')->get();
    }


    public function loadProviderData()
    {
        $this->email = $this->provider->email;
        $this->first_name = $this->provider->first_name;
        $this->last_name = $this->provider->last_name;
        $this->printed_name = $this->provider->printed_name;
        $this->clinic_name = $this->provider->clinic_name;
        $this->npi = $this->provider->{'NPI#'};
        $this->lic = $this->provider->{'LIC#'};
        $this->dea = $this->provider->{'DEA#'};
        $this->phone = $this->provider->phone;
        $this->fax = $this->provider->fax;
        $this->address = $this->provider->address;
        $this->city = $this->provider->city;
        $this->state_id = $this->provider->state_id; // Use the actual state_id from database
        $this->zip = $this->provider->zip;
    }

    public function render()
    {
        return view('livewire.provider.provider-form');
    }

    public function nextStep()
    {
        $this->validateCurrentStep();
        if ($this->step < $this->totalSteps) {
            $this->step++;
        }
    }

    public function prevStep()
    {
        if ($this->step > 1) {
            $this->step--;
        }
    }

    public function goToStep($stepNumber)
    {
        // Only allow direct step navigation in edit mode
        if ($this->editMode && $stepNumber >= 1 && $stepNumber <= $this->totalSteps) {
            $this->step = $stepNumber;
        }
    }

    public function rules()
    {
        $rules = [];

        switch ($this->step) {
            case 1:
                $rules = [
                    'email' => 'required|email|unique:users,email' . ($this->editMode ? ',' . $this->provider->id : ''),
                    'first_name' => 'required|max:256|regex:/^[A-Za-z\s]+$/',
                    'last_name' => 'required|max:256|regex:/^[A-Za-z\s]+$/',
                    'printed_name' => 'required|max:100',
                    'clinic_name' => 'nullable|max:50',
                ];
                break;

            case 2:
                $rules = [
                    'npi' => 'required|digits:10|unique:users,NPI#' . ($this->editMode ? ',' . $this->provider->id : ''),
                    'lic' => [
                        'nullable',
                        function ($attribute, $value, $fail) {
                            if ($value && strlen(str_replace('-', '', $value)) < 7) {
                                $fail('The LIC# must have at least 7 characters excluding dashes.');
                            }
                        },
                    ],
                    'dea' => [
                        'nullable',
                        function ($attribute, $value, $fail) {
                            if ($value) {
                                $cleanValue = preg_replace('/[^a-zA-Z0-9]/', '', $value);
                                if (!preg_match('/^[a-zA-Z]{2}[0-9]{7}$/', $cleanValue)) {
                                    $fail('DEA must be 9 characters: two letters followed by seven digits.');
                                }
                            }
                        }
                    ],
                ];
                break;

            case 3:
                $rules = [
                    'phone' => 'nullable|max:15',
                    'fax' => 'nullable|regex:/^[2-9][0-9]{9}$/',
                    'address' => 'required|string|max:255',
                    'city' => 'required|string|max:255',
                    'state_id' => 'nullable',
                    'zip' => 'required|regex:/^\d{5}(-\d{4})?$/',
                ];

                break;

            case 4:
                if (!$this->editMode) {
                    $rules['signature'] = 'required|file|max:2048|mimes:jpg,png,gif,webp,bmp|max:2048';
                } else {
                    $rules['signature'] = 'nullable|file|max:2048|mimes:jpg,png,gif,webp,bmp|max:2048';
                }
                break;
        }

        return $rules;
    }

    private function validateCurrentStep()
    {
        $this->validate($this->rules());
    }

    public function submit()
    {
        $this->validateCurrentStep();

        try {
            if ($this->editMode) {
                dd($this->all());
                $this->updateProvider();
            } else {
                dd($this->all());
                $this->createProvider();
            }

            session()->flash('success-message', $this->editMode ? 'Provider updated successfully!' : 'Provider created successfully!');
            return redirect()->route('users.index');
        } catch (\Exception $e) {
            Log::error('Provider save error: ' . $e->getMessage());
            session()->flash('error-message', 'An error occurred while saving the provider. Please try again.');
        }
    }

    private function createProvider()
    {
        $signaturePath = null;
        if ($this->signature) {
            $signaturePath = $this->signature->store('signatures', 'public');
        }

        User::create([
            'email' => $this->email,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'printed_name' => $this->printed_name,
            'clinic_name' => $this->clinic_name,
            'NPI#' => $this->npi,
            'LIC#' => $this->lic,
            'DEA#' => $this->dea,
            'phone' => $this->phone,
            'fax' => $this->fax,
            'address' => $this->address,
            'city' => $this->city,
            'state_id' => $this->state_id,
            'zip' => $this->zip,
            'signature' => $signaturePath,
            'password' => Hash::make($this->password),
            'role' => User::ROLE_PROVIDER,
            'is_active' => 1,
        ]);
    }

    private function updateProvider()
    {
        $updateData = [
            'email' => $this->email,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'printed_name' => $this->printed_name,
            'clinic_name' => $this->clinic_name,
            'NPI#' => $this->npi,
            'LIC#' => $this->lic,
            'DEA#' => $this->dea,
            'phone' => $this->phone,
            'fax' => $this->fax,
            'address' => $this->address,
            'city' => $this->city,
            'state_id' => $this->state_id,
            'zip' => $this->zip,
        ];

        if ($this->signature) {
            // Delete old signature if exists
            if ($this->provider->signature) {
                Storage::disk('public')->delete($this->provider->signature);
            }
            $updateData['signature'] = $this->signature->store('signatures', 'public');
        }

        $this->provider->update($updateData);
    }
}
