# Multi-Step Form Generator

This command allows you to generate reusable multi-step Livewire forms with configurable steps.

## Command Usage

```bash
php artisan make:multistep-form {name} {steps} [options]
```

### Parameters

- `name`: The name of the form (e.g., UserForm, ProductForm, ProviderForm)
- `steps`: Number of steps for the form (1-10)

### Options

- `--step-labels=`: Comma-separated step labels (optional)
- `--namespace=`: Custom namespace (optional, defaults to App\Http\Livewire)
- `--folder=`: Custom folder name within livewire directory (optional, defaults to kebab-case form name)

## Examples

### Basic Usage
```bash
# Generate a 4-step form with default labels
php artisan make:multistep-form UserForm 4
```

### With Custom Step Labels
```bash
# Generate a 3-step form with custom labels
php artisan make:multistep-form ProductForm 3 --step-labels="Basic Info,Pricing,Images"
```

### With Custom Namespace
```bash
# Generate form in a specific namespace
php artisan make:multistep-form AdminUserForm 5 --namespace="App\Http\Livewire\Admin"
```

### With Custom Folder
```bash
# Generate form in a specific folder within livewire directory
php artisan make:multistep-form UserForm 4 --folder="user"
```

### Combined Options
```bash
# Generate form with all custom options
php artisan make:multistep-form ProviderForm 4 --step-labels="Basic Information,Identification,Contact and Address,Signature Upload" --folder="provider"
```

## Generated Files

The command generates the following files:

1. **Livewire Component**: `app/Http/Livewire/{folder}/{FormName}.php`
2. **Parent Blade Template**: `resources/views/livewire/{folder}/{form-name}.blade.php`
3. **Step Blade Files**: `resources/views/livewire/{folder}/{form-name}-steps/step{N}.blade.php`

**Note**: All files are organized within a folder structure to keep related forms grouped together.

## Folder Structure Benefits

- **Organization**: Each form gets its own folder, preventing clutter in the main livewire directory
- **Maintainability**: Related files are grouped together for easier maintenance
- **Scalability**: Easy to manage multiple forms without naming conflicts
- **Team Collaboration**: Clear separation makes it easier for teams to work on different forms

## Features

### Dynamic Step Configuration
- Configurable number of steps (1-10)
- Custom step labels
- Clickable step navigation in edit mode
- Automatic step validation

### Built-in Functionality
- Step navigation (next/previous)
- Form validation per step
- Edit mode support
- File upload support (WithFileUploads trait)
- Loading states
- Error handling

### Responsive Design
- Bootstrap-based UI
- Purple theme colors
- Mobile-friendly step indicators
- Professional styling

## Customization

### Adding Form Fields

1. **In the Component Class** (`app/Http/Livewire/{FormName}.php`):
```php
// Add public properties for your fields
public $email, $first_name, $last_name;
public $phone, $address, $city;
```

2. **In Step Blade Files** (`resources/views/livewire/{form-name}-steps/step{N}.blade.php`):
```php
<x-form.input.text label="Email" labelRequired="1" model="email" type="email" />
<x-form.input.text label="First Name" labelRequired="1" model="first_name" />
```

### Adding Validation Rules

In the `validateCurrentStep()` method:
```php
switch ($this->step) {
    case 1:
        $rules = [
            'email' => 'required|email|unique:users,email',
            'first_name' => 'required|string|max:255',
        ];
        break;
    case 2:
        $rules = [
            'phone' => 'required|string|max:20',
            'address' => 'required|string|max:255',
        ];
        break;
}
```

### Implementing Save Logic

Update the `createModel()` and `updateModel()` methods:
```php
private function createModel()
{
    User::create([
        'email' => $this->email,
        'first_name' => $this->first_name,
        'last_name' => $this->last_name,
        // ... other fields
    ]);
}
```

## Default Step Labels

When no custom labels are provided, the command uses these defaults:
1. Basic Information
2. Identification
3. Contact and Address
4. Signature Upload
5. Additional Details
6. Preferences
7. Review
8. Confirmation
9. Final Step
10. Completion

## Integration with Existing Forms

You can use this generator to convert existing single-step forms into multi-step forms:

1. Generate the multi-step structure
2. Copy existing form fields to appropriate step files
3. Move validation rules to the step-based validation method
4. Update the save logic

## Best Practices

1. **Logical Step Grouping**: Group related fields together in steps
2. **Validation**: Validate each step before allowing navigation
3. **User Experience**: Use descriptive step labels
4. **Error Handling**: Implement proper error messages and logging
5. **Testing**: Test all step transitions and validation scenarios

## Troubleshooting

### Common Issues

1. **Files not generated**: Check directory permissions
2. **Namespace errors**: Ensure the namespace path exists
3. **Step labels mismatch**: Number of labels must match number of steps

### File Locations

If files are generated in unexpected locations, check:
- Current working directory
- Namespace configuration
- Laravel application structure
