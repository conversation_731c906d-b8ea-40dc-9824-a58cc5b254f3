@php
    use App\Models\User;
@endphp
<div>
    <x-form.input.text label="Phone" labelRequired="0" model="phone" type="tel" placeholder="Enter phone number" />
    <x-form.input.text label="Fax" labelRequired="0" model="fax" type="tel" placeholder="Enter fax number" />
    <x-form.input.text label="Address" labelRequired="1" model="address" placeholder="Enter street address" />
    <x-form.input.text label="City" labelRequired="1" model="city" placeholder="Enter city" />
    <x-form.input.drop-down label="State" labelRequired="0" model="state_id" id="state_id" placeholder="Select State">
        <option value="">Select State</option>
        @foreach ($states as $stateOption)
            <option value="{{ $stateOption->id }}"
                {{ $state_id == $stateOption->id ? 'selected' : '' }}>
                {{ $stateOption->name }}
            </option>
        @endforeach
    </x-form.input.drop-down>
    <x-form.input.text label="ZIP Code" labelRequired="1" model="zip"
        placeholder="Enter ZIP code (12345 or 12345-6789)" />
</div>
@push('scripts')
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
    <script>
        document.addEventListener("livewire:load", function() {

            // Initialize Select2 for state dropdown
            function createStateDropdown() {
                $('#state_id').select2({
                    placeholder: "Select State",
                }).on('change', function(e) {
                    @this.set('state_id', $(e.target).val());
                });
            }

            createStateDropdown();

            // Re-initialize Select2 after Livewire updates the DOM
            window.livewire.on('contentChanged', function() {
                createStateDropdown();
            });

        });
    </script>
@endpush
