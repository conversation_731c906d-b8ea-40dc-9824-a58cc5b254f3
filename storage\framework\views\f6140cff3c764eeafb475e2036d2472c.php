<div>
    <?php if (isset($component)) { $__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.row','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.row'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
        <div class="col">
            <?php if (isset($component)) { $__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card.body','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('card.body'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                <!-- Step Progress Indicator -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-center" style="gap: 4rem;">
                            <?php $__currentLoopData = $steps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stepNumber => $stepLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="text-center">
                                    <?php if($editMode): ?>
                                        <button type="button" wire:click="goToStep(<?php echo e($stepNumber); ?>)"
                                            class="btn p-0 border-0" style="background: none;">
                                            <div class="d-flex flex-column align-items-center">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                    style="width: 50px; height: 50px; background-color: <?php echo e($step === $stepNumber ? '#e9c5ff' : '#d1d5db'); ?>; color: <?php echo e($step === $stepNumber ? '#7c3aed' : '#6b7280'); ?>; font-weight: 600; font-size: 1.1rem;">
                                                    <?php echo e($stepNumber); ?>

                                                </div>
                                                <div class="mt-2"
                                                    style="font-size: 0.875rem; color: <?php echo e($step === $stepNumber ? '#7c3aed' : '#6b7280'); ?>; white-space: nowrap;">
                                                    <?php echo e($stepLabel); ?>

                                                </div>
                                            </div>
                                        </button>
                                    <?php else: ?>
                                        <div class="d-flex flex-column align-items-center">
                                            <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                style="width: 50px; height: 50px; background-color: <?php echo e($step === $stepNumber ? '#e9c5ff' : '#d1d5db'); ?>; color: <?php echo e($step === $stepNumber ? '#7c3aed' : '#6b7280'); ?>; font-weight: 600; font-size: 1.1rem;">
                                                <?php echo e($stepNumber); ?>

                                            </div>
                                            <div class="mt-2"
                                                style="font-size: 0.875rem; color: <?php echo e($step === $stepNumber ? '#7c3aed' : '#6b7280'); ?>; white-space: nowrap;">
                                                <?php echo e($stepLabel); ?>

                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>

                <!-- Step Content -->
                <?php if($step === 1): ?>
                    <?php echo $__env->make('livewire.provider.provider-steps.step1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php elseif($step === 2): ?>
                    <?php echo $__env->make('livewire.provider.provider-steps.step2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php elseif($step === 3): ?>
                    <?php echo $__env->make('livewire.provider.provider-steps.step3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php elseif($step === 4): ?>
                    <?php echo $__env->make('livewire.provider.provider-steps.step4', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6)): ?>
<?php $attributes = $__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6; ?>
<?php unset($__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6)): ?>
<?php $component = $__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6; ?>
<?php unset($__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6); ?>
<?php endif; ?>

            

            <?php if (isset($component)) { $__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card.footer','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('card.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                <div class="d-flex justify-content-between">
                    <?php if($step > 1): ?>
                        <button type="button" class="btn btn-secondary" wire:click="prevStep"
                            wire:loading.attr="disabled">
                            <i class="fas fa-arrow-left me-1"></i> Previous
                        </button>
                    <?php else: ?>
                        <div></div>
                    <?php endif; ?>

                    <?php if($step < $totalSteps): ?>
                        <button type="button" class="btn btn-primary" wire:click="nextStep"
                            wire:loading.attr="disabled">
                            Next <i class="fas fa-arrow-right ms-1"></i>
                        </button>
                    <?php else: ?>
                        <button type="button" class="btn btn-success" wire:click="submit" wire:loading.attr="disabled"
                            wire:loading.class="disabled">
                            <span wire:loading.remove wire:target="submit">
                                <i class="fas fa-save me-1"></i>
                                <?php echo e($editMode ? 'Update Provider' : 'Create Provider'); ?>

                            </span>
                            <span wire:loading wire:target="submit">
                                <i class="fas fa-spinner fa-spin me-1"></i>
                                <?php echo e($editMode ? 'Updating...' : 'Creating...'); ?>

                            </span>
                        </button>
                    <?php endif; ?>
                </div>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0)): ?>
<?php $attributes = $__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0; ?>
<?php unset($__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0)): ?>
<?php $component = $__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0; ?>
<?php unset($__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0); ?>
<?php endif; ?>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c)): ?>
<?php $attributes = $__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c; ?>
<?php unset($__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c)): ?>
<?php $component = $__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c; ?>
<?php unset($__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c); ?>
<?php endif; ?>
</div>

<script>
    document.addEventListener("livewire:load", function() {

        // Initialize Select2 for state dropdown
        function createStateDropdown() {
            $('#state_id').select2({
                placeholder: "Select State",
            }).on('change', function(e) {
                window.livewire.find('<?php echo e($_instance->id); ?>').set('state_id', $(e.target).val());
            });
        }

        // Initialize Select2 for Default Dispatch Method dropdown
        function createDispatchMethodDropdown() {
            $('#default_dispatch_method').select2({
                placeholder: "Select Method",
                width: '100%'
                // Search enabled by default
            }).on('change', function(e) {
                window.livewire.find('<?php echo e($_instance->id); ?>').set('default_dispatch_method', $(e.target).val());
            });
        }

        createStateDropdown();
        createDispatchMethodDropdown();

        // Re-initialize Select2 after Livewire updates the DOM
        window.livewire.on('contentChanged', function() {
            createStateDropdown();
            createDispatchMethodDropdown();
        });

    });
</script>
<?php /**PATH C:\KodeCreators\newlife-panel\resources\views/livewire/provider/provider-form.blade.php ENDPATH**/ ?>