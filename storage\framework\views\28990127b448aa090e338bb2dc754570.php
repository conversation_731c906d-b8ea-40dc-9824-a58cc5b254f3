<div>
    <?php if($editMode && $provider && $provider->signature): ?>
        <div class="mb-3">
            <label class="form-label">Current Signature</label>
            <div class="border p-3 rounded">
                <img src="<?php echo e(Storage::url($provider->signature)); ?>" alt="Current Signature" class="img-fluid" style="max-height: 150px;">
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($component)) { $__componentOriginale16b80fe247f0459e7f5e2e4648a4335 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale16b80fe247f0459e7f5e2e4648a4335 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.image','data' => ['labelRequired' => ''.e($editMode ? '0' : '1').'','preview' => $signature,'label' => ''.e($editMode ? 'Upload New Signature (Optional)' : 'Upload Signature').'','previewUrl' => $signature ? optional($signature)->temporaryUrl() : null,'model' => 'signature']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.image'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['labelRequired' => ''.e($editMode ? '0' : '1').'','preview' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($signature),'label' => ''.e($editMode ? 'Upload New Signature (Optional)' : 'Upload Signature').'','previewUrl' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($signature ? optional($signature)->temporaryUrl() : null),'model' => 'signature']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale16b80fe247f0459e7f5e2e4648a4335)): ?>
<?php $attributes = $__attributesOriginale16b80fe247f0459e7f5e2e4648a4335; ?>
<?php unset($__attributesOriginale16b80fe247f0459e7f5e2e4648a4335); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale16b80fe247f0459e7f5e2e4648a4335)): ?>
<?php $component = $__componentOriginale16b80fe247f0459e7f5e2e4648a4335; ?>
<?php unset($__componentOriginale16b80fe247f0459e7f5e2e4648a4335); ?>
<?php endif; ?>

    <?php if($editMode): ?>
        <small class="text-muted">Leave empty to keep current signature</small>
    <?php endif; ?>
</div>
<?php /**PATH C:\KodeCreators\newlife-panel\resources\views/livewire/provider/provider-steps/step4.blade.php ENDPATH**/ ?>