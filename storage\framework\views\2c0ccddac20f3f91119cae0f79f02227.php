<?php
// No imports needed
?>

<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
'preview' => false,
'previewUrl' => null,
'previewLabel' => 'Preview',
'oldPreviewLabel' => 'Old Preview',
'oldPreview' => false,
'label' => 'Label',
'labelRequired' => false,
'model',
'helper' => false,
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
'preview' => false,
'previewUrl' => null,
'previewLabel' => 'Preview',
'oldPreviewLabel' => 'Old Preview',
'oldPreview' => false,
'label' => 'Label',
'labelRequired' => false,
'model',
'helper' => false,
]); ?>
<?php foreach (array_filter(([
'preview' => false,
'previewUrl' => null,
'previewLabel' => 'Preview',
'oldPreviewLabel' => 'Old Preview',
'oldPreview' => false,
'label' => 'Label',
'labelRequired' => false,
'model',
'helper' => false,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php if($oldPreview): ?>
<p class="mb-0"><?php echo e($oldPreviewLabel); ?></p>
<?php
$imageUrl = '';
if ($oldPreview) {
if (str_starts_with($oldPreview, 'http')) {
$imageUrl = $oldPreview;
} else {
// Simple direct URL approach
$imageUrl = '/storage/' . $oldPreview;
}
}
?>
<img class="mb-6 border" style="object-fit: contain; max-width: 200px" src="<?php echo e($imageUrl); ?>">
<?php endif; ?>

<?php if($preview): ?>
<p class="mb-0"><?php echo e($previewLabel); ?></p>
<img class="mb-6 border" style="object-fit: contain; max-width: 200px" src="<?php echo e($previewUrl); ?>">
<?php endif; ?>

<div class="form-group" x-data="{ isUploading: false, progress: 0, hasError: false, errorMessage: '', showUploadingMessage: false }"
    x-init="
        $watch('isUploading', value => {
            if (value) {
                // Set a global variable to track upload state
                window.signatureUploading = true;
                // Dispatch event when upload starts
                document.dispatchEvent(new CustomEvent('signature-upload-start', { detail: { model: '<?php echo e($model); ?>' } }));
            } else {
                // Set a timeout to ensure the upload is really complete
                setTimeout(() => {
                    window.signatureUploading = false;
                    // Dispatch event when upload finishes
                    document.dispatchEvent(new CustomEvent('signature-upload-finish', { detail: { model: '<?php echo e($model); ?>' } }));
                }, 500);
            }
        })
    "
    x-on:livewire-upload-start="isUploading = true; hasError = false; errorMessage = '';"
    x-on:livewire-upload-finish="isUploading = false; showUploadingMessage = false;"
    x-on:livewire-upload-error="isUploading = false; hasError = true; errorMessage = 'Upload failed. Please try again with a smaller image.'; showUploadingMessage = false; document.dispatchEvent(new CustomEvent('signature-upload-error', { detail: { model: '<?php echo e($model); ?>' } }))"
    x-on:livewire-upload-progress="progress = $event.detail.progress; document.dispatchEvent(new CustomEvent('signature-upload-progress', { detail: { progress: progress, model: '<?php echo e($model); ?>' } }))"
    x-on:click="if(isUploading) { showUploadingMessage = true; setTimeout(() => { showUploadingMessage = false }, 3000) }">

    <?php if (isset($component)) { $__componentOriginal306f477fe089d4f950325a3d0a498c1c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal306f477fe089d4f950325a3d0a498c1c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.label','data' => ['label' => ''.e($label).'','labelRequired' => ''.e($labelRequired).'','for' => ''.e($model).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.label'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => ''.e($label).'','labelRequired' => ''.e($labelRequired).'','for' => ''.e($model).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal306f477fe089d4f950325a3d0a498c1c)): ?>
<?php $attributes = $__attributesOriginal306f477fe089d4f950325a3d0a498c1c; ?>
<?php unset($__attributesOriginal306f477fe089d4f950325a3d0a498c1c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal306f477fe089d4f950325a3d0a498c1c)): ?>
<?php $component = $__componentOriginal306f477fe089d4f950325a3d0a498c1c; ?>
<?php unset($__componentOriginal306f477fe089d4f950325a3d0a498c1c); ?>
<?php endif; ?>

    <div class="custom-file">
        <input wire:model="<?php echo e($model); ?>" id="<?php echo e($model); ?>" type="file"
            accept="image/jpeg,image/png,image/gif,image/webp,image/bmp" class="custom-file-input"
            x-bind:disabled="isUploading" />
        <label class="custom-file-label" for="<?php echo e($model); ?>" x-bind:class="{ 'disabled': isUploading }">
            <span x-show="!isUploading">
                <?php if($preview): ?>
                Image Selected
                <?php else: ?>
                Choose file (max 2MB)
                <?php endif; ?>
            </span>
            <span x-show="isUploading">Upload in progress...</span>
        </label>
    </div>

    <div x-show="isUploading" class="progress mt-4" style="display: none;">
        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
            x-bind:style="['width: ' + progress + '%']" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100"></div>
    </div>

    <div x-show="hasError" class="alert alert-danger mt-2" style="display: none;" x-text="errorMessage"></div>

    

    <small class="form-text text-muted mt-2">
        Supported formats: JPG, PNG, GIF, WEBP, BMP. Maximum size: 2MB.
    </small>

    <?php if (isset($component)) { $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.error','data' => ['model' => ''.e($model).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['model' => ''.e($model).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $attributes = $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $component = $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
    <?php if (isset($component)) { $__componentOriginalb2cd6c3734aae9f8ed791b2c94d2d3ba = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb2cd6c3734aae9f8ed791b2c94d2d3ba = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.helper','data' => ['helper' => ''.e($helper).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.helper'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['helper' => ''.e($helper).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb2cd6c3734aae9f8ed791b2c94d2d3ba)): ?>
<?php $attributes = $__attributesOriginalb2cd6c3734aae9f8ed791b2c94d2d3ba; ?>
<?php unset($__attributesOriginalb2cd6c3734aae9f8ed791b2c94d2d3ba); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb2cd6c3734aae9f8ed791b2c94d2d3ba)): ?>
<?php $component = $__componentOriginalb2cd6c3734aae9f8ed791b2c94d2d3ba; ?>
<?php unset($__componentOriginalb2cd6c3734aae9f8ed791b2c94d2d3ba); ?>
<?php endif; ?>
</div>

<?php if (! $__env->hasRenderedOnce('d649fce0-06bc-4b80-9125-6e4b1b03561a')): $__env->markAsRenderedOnce('d649fce0-06bc-4b80-9125-6e4b1b03561a'); ?>
<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.8.2/dist/alpine.min.js" defer></script>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('styles'); ?>
<style>
    .custom-file-label.disabled {
        background-color: #f3f6f9;
        cursor: not-allowed;
        opacity: 0.7;
    }

    input[type="file"]:disabled+.custom-file-label {
        background-color: #f3f6f9;
        cursor: not-allowed;
        opacity: 0.7;
    }
</style>
<?php $__env->stopPush(); ?>
<?php endif; ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/components/form/input/image.blade.php ENDPATH**/ ?>